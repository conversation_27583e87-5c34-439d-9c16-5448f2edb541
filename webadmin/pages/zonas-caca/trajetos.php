<?php
require_once '../../includes/config.php';
require_once '../../includes/auth.php';

// Check if user is logged in and is admin
if (!is_logged_in()) {
    header('Location: ' . SITE_URL . '/pages/auth/login.php');
    exit();
}

if (!isAdmin()) {
    header('Location: ' . SITE_URL . '/pages/dashboard/');
    exit();
}

$error = '';
$success = '';
$trajetos = [];

// Initialize Firebase
$firebase = new Firebase(FIREBASE_PROJECT_ID, FIREBASE_API_KEY);

// PERFORMANCE OPTIMIZATION: Set high limits to show all data while maintaining performance
$MAX_TRAJETOS_LIMIT = 5000;  // High limit to ensure all trajectories are shown
$MAX_CONTACTS_LIMIT = 10000; // High limit to ensure all contacts are loaded

// Handle GPS trajectory analysis action
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'analyze_gps') {
    $trajetoId = $_POST['trajeto_id'] ?? '';
    if (!empty($trajetoId)) {
        try {
            // Get trajectory data
            $trajeto = null;
            $collection = null;
            
            // Try gestorMobile_trajetos first
            try {
                $trajeto = $firebase->getDocument('gestorMobile_trajetos', $trajetoId);
                $collection = 'gestorMobile_trajetos';
            } catch (Exception $e) {
                // Try zonas collection
                try {
                    $trajeto = $firebase->getDocument('zonas', $trajetoId);
                    $collection = 'zonas';
                } catch (Exception $e2) {
                    throw new Exception("Trajeto não encontrado");
                }
            }
            
            if ($trajeto) {
                $analysis = analyzeGPSTrajectory($trajeto);
                
                // Return JSON response for AJAX
                if (isset($_POST['ajax'])) {
                    header('Content-Type: application/json');
                    echo json_encode([
                        'success' => true,
                        'analysis' => $analysis,
                        'trajectory' => $trajeto
                    ]);
                    exit;
                }
            }
        } catch (Exception $e) {
            if (isset($_POST['ajax'])) {
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => false,
                    'error' => $e->getMessage()
                ]);
                exit;
            }
            $error = 'Erro ao analisar trajeto: ' . $e->getMessage();
        }
    }
}

// Handle delete action
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'delete') {
    $trajetoId = $_POST['trajeto_id'] ?? '';
    if (!empty($trajetoId)) {
        try {
            // First, determine which collection contains this trajectory
            $trajectoryCollection = null;
            $isGpsTrajectory = false;
            
            // Check if it exists in 'zonas' collection (manual trajectories)
            try {
                $manualTrajeto = $firebase->getDocument('zonas', $trajetoId);
                if ($manualTrajeto) {
                    $trajectoryCollection = 'zonas';
                }
            } catch (Exception $e) {
                // Document not found in zonas collection, continue checking
            }
            
            // If not found in zonas, check 'gestorMobile_trajetos' collection (GPS trajectories)
            if (!$trajectoryCollection) {
                try {
                    $gpsTrajeto = $firebase->getDocument('gestorMobile_trajetos', $trajetoId);
                    if ($gpsTrajeto) {
                        $trajectoryCollection = 'gestorMobile_trajetos';
                        $isGpsTrajectory = true;
                    }
                } catch (Exception $e) {
                    // Document not found in gestorMobile_trajetos collection either
                }
            }
            
            if (!$trajectoryCollection) {
                throw new Exception("Trajeto não encontrado em nenhuma coleção");
            }
            
            // Delete associated contacts first - OPTIMIZED: Use limited pagination
            $deletedContacts = 0;
            $pageToken = null;
            $contactsProcessed = 0;
            
            do {
                $pageContacts = $firebase->listDocuments('contacts', $pageToken, 1000);
                
                // Extract pagination token if present
                $pageToken = $pageContacts['_nextPageToken'] ?? null;
                unset($pageContacts['_nextPageToken']);
                
                foreach ($pageContacts as $contact) {
                    if (isset($contact['trajectoryId']) && $contact['trajectoryId'] === $trajetoId) {
                        if ($firebase->deleteDocument('contacts', $contact['id'])) {
                            $deletedContacts++;
                        }
                    }
                }
                
                $contactsProcessed += count($pageContacts);
                
                // Safety break to prevent infinite loops
                if ($contactsProcessed > $MAX_CONTACTS_LIMIT) {
                    break;
                }
                
            } while ($pageToken);
            
            // For GPS trajectories, also delete associated mobile contacts
            if ($isGpsTrajectory) {
                $pageToken = null;
                $mobileContactsProcessed = 0;
                
                do {
                    $pageMobileContacts = $firebase->listDocuments('gestorMobile_contacts', $pageToken, 1000);
                    
                    // Extract pagination token if present
                    $pageToken = $pageMobileContacts['_nextPageToken'] ?? null;
                    unset($pageMobileContacts['_nextPageToken']);
                    
                    foreach ($pageMobileContacts as $mobileContact) {
                        if (isset($mobileContact['trajectoryId']) && $mobileContact['trajectoryId'] === $trajetoId) {
                            if ($firebase->deleteDocument('gestorMobile_contacts', $mobileContact['id'])) {
                                $deletedContacts++;
                            }
                        }
                    }
                    
                    $mobileContactsProcessed += count($pageMobileContacts);
                    
                    // Safety break to prevent infinite loops
                    if ($mobileContactsProcessed > $MAX_CONTACTS_LIMIT) {
                        break;
                    }
                    
                } while ($pageToken);
            }
            
            // Delete the trajeto from the correct collection
            $result = $firebase->deleteDocument($trajectoryCollection, $trajetoId);
            if ($result) {
                $success = "Trajeto eliminado com sucesso" . ($deletedContacts > 0 ? " (incluindo $deletedContacts contactos)" : "") . ".";
            } else {
                $error = 'Erro ao eliminar o trajeto.';
            }
        } catch (Exception $e) {
            $error = 'Erro ao eliminar trajeto: ' . $e->getMessage();
        }
    }
}

// PERFORMANCE OPTIMIZATION: Get trajetos with pagination limits
try {
    // Get manual trajectories from 'zonas' collection with LIMIT
    $manualTrajetos = [];
    $pageToken = null;
    $zonesProcessed = 0;
    
    do {
        $pageZones = $firebase->listDocuments('zonas', $pageToken, 1000);
        
        // Extract pagination token if present
        $pageToken = $pageZones['_nextPageToken'] ?? null;
        unset($pageZones['_nextPageToken']);
        
        // Merge results
        $manualTrajetos = array_merge($manualTrajetos, $pageZones);
        $zonesProcessed += count($pageZones);
        
        // OPTIMIZATION: Stop after reasonable limit
        if ($zonesProcessed >= $MAX_TRAJETOS_LIMIT) {
            break;
        }
        
    } while ($pageToken);
    
    // Get GPS trajectories from 'gestorMobile_trajetos' collection with LIMIT
    $gpsTrajetos = [];
    $pageToken = null;
    $gpsTrajetosProcessed = 0;
    
    do {
        $pageGpsTrajetos = $firebase->listDocuments('gestorMobile_trajetos', $pageToken, 1000);
        
        // Extract pagination token if present
        $pageToken = $pageGpsTrajetos['_nextPageToken'] ?? null;
        unset($pageGpsTrajetos['_nextPageToken']);
        
        // Merge results
        $gpsTrajetos = array_merge($gpsTrajetos, $pageGpsTrajetos);
        $gpsTrajetosProcessed += count($pageGpsTrajetos);
        
        // OPTIMIZATION: Stop after reasonable limit
        if ($gpsTrajetosProcessed >= $MAX_TRAJETOS_LIMIT) {
            break;
        }
        
    } while ($pageToken);

    // PERFORMANCE OPTIMIZATION: Enhanced user lookup cache with batch processing
    $userLookupCache = [];
    $userBatchQueue = [];
    
    // Process manual trajectories
    if ($manualTrajetos) {
        foreach ($manualTrajetos as $docId => $doc) {
            // Only include documents that have coordinates (are actual trajetos)
            if (isset($doc['coordinates']) && is_array($doc['coordinates']) && !empty($doc['coordinates'])) {
                // Create a clean copy to avoid reference issues
                $trajetoData = $doc;
                
                // Ensure the document has an ID
                if (!isset($trajetoData['id'])) {
                    $trajetoData['id'] = $docId;
                }
                
                // Add trajectory type
                $trajetoData['type'] = 'Manual';
                
                // Queue user lookup for batch processing
                if (!empty($trajetoData['createdBy']) && empty($trajetoData['createdByEmail'])) {
                    $userBatchQueue[] = $trajetoData['createdBy'];
                }
                
                // Add to trajetos array
                $trajetos[] = $trajetoData;
            }
        }
    }
    
    // Process GPS trajectories
    if ($gpsTrajetos) {
        foreach ($gpsTrajetos as $docId => $doc) {
            // Only include completed GPS trajectories
            if (isset($doc['status']) && $doc['status'] === 'completed') {
                // GPS trajectories should have coordinates, route, or pathCoordinates data
                if ((isset($doc['coordinates']) && is_array($doc['coordinates']) && !empty($doc['coordinates'])) ||
                    (isset($doc['route']) && is_array($doc['route']) && !empty($doc['route'])) ||
                    (isset($doc['pathCoordinates']) && is_array($doc['pathCoordinates']) && !empty($doc['pathCoordinates']))) {
                    
                    // Create a clean copy to avoid reference issues
                    $trajetoData = $doc;
                    
                    // Ensure the document has an ID
                    if (!isset($trajetoData['id'])) {
                        $trajetoData['id'] = $docId;
                    }
                    
                    // Add trajectory type
                    $trajetoData['type'] = 'GPS';
                    
                    // Standardize coordinates field for GPS trajectories
                    if (!isset($trajetoData['coordinates'])) {
                        if (isset($trajetoData['route'])) {
                            $trajetoData['coordinates'] = $trajetoData['route'];
                        } elseif (isset($trajetoData['pathCoordinates'])) {
                            $trajetoData['coordinates'] = $trajetoData['pathCoordinates'];
                        }
                    }
                    
                    // Standardize distance field for GPS trajectories (convert from meters to km string)
                    if (!isset($trajetoData['distance']) && isset($trajetoData['totalDistance'])) {
                        $totalDistance = $trajetoData['totalDistance'];
                        if ($totalDistance > 0) {
                            $trajetoData['distance'] = number_format($totalDistance / 1000, 2) . ' km';
                        } else {
                            $trajetoData['distance'] = '0 km';
                        }
                    }
                    
                    // Standardize creator fields for GPS trajectories
                    if (!isset($trajetoData['createdBy']) && isset($trajetoData['userId'])) {
                        $trajetoData['createdBy'] = $trajetoData['userId'];
                    }
                    if (!isset($trajetoData['createdByEmail']) && isset($trajetoData['userEmail'])) {
                        $trajetoData['createdByEmail'] = $trajetoData['userEmail'];
                    }
                    
                    // Queue user lookup for batch processing
                    if (!empty($trajetoData['createdBy']) && empty($trajetoData['createdByEmail'])) {
                        $userBatchQueue[] = $trajetoData['createdBy'];
                    }
                    
                    // Standardize name field for GPS trajectories
                    if (!isset($trajetoData['name'])) {
                        if (isset($trajetoData['zoneName'])) {
                            $trajetoData['name'] = $trajetoData['zoneName'];
                        } else {
                            // Fallback name for GPS trajectories without zoneName
                            $dateForName = $trajetoData['createdAt'] ?? $trajetoData['startTime'] ?? null;
                            $trajetoData['name'] = 'Trajeto GPS - ' . ($dateForName ? date('d/m/Y', strtotime($dateForName)) : 'Sem data');
                        }
                    }
                    
                    // Standardize date field for GPS trajectories (prefer startTime over createdAt for display)
                    if (!isset($trajetoData['createdAt']) && isset($trajetoData['startTime'])) {
                        $trajetoData['createdAt'] = $trajetoData['startTime'];
                    }
                    
                    // Add to trajetos array
                    $trajetos[] = $trajetoData;
                }
            }
        }
    }
    
    // PERFORMANCE OPTIMIZATION: Batch process user lookups
    if (!empty($userBatchQueue)) {
        $uniqueUserIds = array_unique($userBatchQueue);
        
        // Process in batches of 50 to avoid rate limiting
        $batchSize = 50;
        $userBatches = array_chunk($uniqueUserIds, $batchSize);
        
        foreach ($userBatches as $batch) {
            foreach ($batch as $userId) {
                if (!isset($userLookupCache[$userId])) {
                    $userInfo = lookupUserInfo($userId, $firebase);
                    $userLookupCache[$userId] = $userInfo;
                }
            }
            
            // Add small delay between batches to prevent rate limiting
            if (count($userBatches) > 1) {
                usleep(100000); // 100ms delay
            }
        }
    }
    
    // Apply cached user information to trajetos
    foreach ($trajetos as &$trajeto) {
        if (!empty($trajeto['createdBy']) && empty($trajeto['createdByEmail']) && isset($userLookupCache[$trajeto['createdBy']])) {
            $userInfo = $userLookupCache[$trajeto['createdBy']];
            if ($userInfo) {
                $trajeto['createdByEmail'] = $userInfo['email'];
                if ($userInfo['name']) {
                    $trajeto['createdByName'] = $userInfo['name'];
                }
            }
        }
    }
    unset($trajeto); // Unset reference to avoid PHP issues
    
    // REAL PERFORMANCE OPTIMIZATION: Load contacts efficiently for displayed trajectories only
    $trajetoIds = array_column($trajetos, 'id');
    $contactCounts = [];
    
    if (!empty($trajetoIds)) {
        // Load contact counts only (not full contact data) for much faster loading
        try {
            $pageToken = null;
            $contactsProcessed = 0;
            
            do {
                $pageContacts = $firebase->listDocuments('contacts', $pageToken, 1000);
                
                $pageToken = $pageContacts['_nextPageToken'] ?? null;
                unset($pageContacts['_nextPageToken']);
                
                // Count contacts per trajectory
                foreach ($pageContacts as $contact) {
                    if (isset($contact['trajectoryId']) && in_array($contact['trajectoryId'], $trajetoIds)) {
                        $contactCounts[$contact['trajectoryId']] = ($contactCounts[$contact['trajectoryId']] ?? 0) + 1;
                    }
                }
                
                $contactsProcessed += count($pageContacts);
                
                if ($contactsProcessed >= $MAX_CONTACTS_LIMIT) {
                    break;
                }
                
            } while ($pageToken);
        } catch (Exception $e) {
            error_log("Error loading contact counts: " . $e->getMessage());
        }
        
        // Load mobile contact counts for GPS trajectories
        try {
            $pageToken = null;
            $mobileContactsProcessed = 0;
            
            do {
                $pageMobileContacts = $firebase->listDocuments('gestorMobile_contacts', $pageToken, 1000);
                
                $pageToken = $pageMobileContacts['_nextPageToken'] ?? null;
                unset($pageMobileContacts['_nextPageToken']);
                
                foreach ($pageMobileContacts as $contact) {
                    if (isset($contact['sessionId'])) {
                        foreach ($trajetos as $trajeto) {
                            if ($trajeto['type'] === 'GPS') {
                                $sessionId = $trajeto['sessionId'] ?? $trajeto['id'];
                                if ($contact['sessionId'] === $sessionId) {
                                    $contactCounts[$trajeto['id']] = ($contactCounts[$trajeto['id']] ?? 0) + 1;
                                    break;
                                }
                            }
                        }
                    }
                }
                
                $mobileContactsProcessed += count($pageMobileContacts);
                
                if ($mobileContactsProcessed >= $MAX_CONTACTS_LIMIT) {
                    break;
                }
                
            } while ($pageToken);
        } catch (Exception $e) {
            error_log("Error loading mobile contact counts: " . $e->getMessage());
        }
    }
    
    // Assign contact counts to trajectories
    foreach ($trajetos as &$trajeto) {
        $trajeto['contactCount'] = $contactCounts[$trajeto['id']] ?? 0;
        $trajeto['contacts'] = []; // Will be loaded when viewing details
    }
    unset($trajeto);
    
} catch (Exception $e) {
    $error = 'Erro ao carregar trajetos: ' . $e->getMessage();
}

// Function to format date
function formatDate($dateString) {
    if (empty($dateString)) return 'N/A';
    
    try {
        // Check if it's a Unix timestamp (numeric string or number)
        if (is_numeric($dateString)) {
            $timestamp = (int)$dateString;
            
            // Check if it's a reasonable timestamp (between 1970 and 2100)
            if ($timestamp > 0 && $timestamp < 4102444800) { // 4102444800 = Jan 1, 2100
                // If timestamp is in milliseconds (13 digits), convert to seconds
                if ($timestamp > 9999999999) {
                    $timestamp = $timestamp / 1000;
                }
                
                $date = new DateTime();
                $date->setTimestamp($timestamp);
                return $date->format('d/m/Y H:i');
            }
        }
        
        // Try to parse as a date string
        $date = new DateTime($dateString);
        return $date->format('d/m/Y H:i');
        
    } catch (Exception $e) {
        // If all else fails, return the original string
        return $dateString;
    }
}

function getTimestamp($dateString) {
    if (empty($dateString)) return 0;
    
    try {
        // Check if it's a Unix timestamp (numeric string or number)
        if (is_numeric($dateString)) {
            $timestamp = (int)$dateString;
            
            // Check if it's a reasonable timestamp (between 1970 and 2100)
            if ($timestamp > 0 && $timestamp < 4102444800) { // 4102444800 = Jan 1, 2100
                // If timestamp is in milliseconds (13 digits), convert to seconds
                if ($timestamp > 9999999999) {
                    $timestamp = $timestamp / 1000;
                }
                return $timestamp;
            }
        }
        
        // Try to parse as a date string and get timestamp
        $date = new DateTime($dateString);
        return $date->getTimestamp();
        
    } catch (Exception $e) {
        // If all else fails, return 0
        return 0;
    }
}

// GPS Trajectory Analysis Function
function analyzeGPSTrajectory($trajeto) {
    $analysis = [
        'quality_score' => 0,
        'issues' => [],
        'recommendations' => [],
        'statistics' => [
            'total_points' => 0,
            'valid_points' => 0,
            'gps_jumps' => 0,
            'max_speed_kmh' => 0,
            'avg_speed_kmh' => 0,
            'total_distance_km' => 0,
            'recalculated_distance_km' => 0,
            'distance_difference_percent' => 0,
            'time_span_minutes' => 0,
            'stationary_points' => 0,
            'analysis_source' => 'trajectory_coordinates' // Default
        ],
        'gps_jumps' => [],
        'speed_violations' => [],
        'quality_rating' => 'Unknown'
    ];
    
    // ENHANCED: Try to get full GPS points from gpsPoints collection first
    $fullGPSPoints = [];
    $sessionId = $trajeto['sessionId'] ?? null;
    
    if ($sessionId) {
        global $firebase;
        try {
            // Load all GPS points and filter by sessionId
            $allGPSPoints = [];
            $pageToken = null;
            
            do {
                $pageGPSPoints = $firebase->listDocuments('gpsPoints', $pageToken, 1000);
                $pageToken = $pageGPSPoints['_nextPageToken'] ?? null;
                unset($pageGPSPoints['_nextPageToken']);
                $allGPSPoints = array_merge($allGPSPoints, $pageGPSPoints);
            } while ($pageToken);
            
            // Filter GPS points by sessionId
            $gpsPointsCollection = [];
            foreach ($allGPSPoints as $point) {
                if (isset($point['sessionId']) && $point['sessionId'] === $sessionId) {
                    $gpsPointsCollection[] = $point;
                }
            }
            
            if (!empty($gpsPointsCollection)) {
                $analysis['statistics']['analysis_source'] = 'full_gps_points';
                $analysis['issues'][] = 'Análise baseada em pontos GPS completos (maior precisão)';
                
                // Convert GPS points to coordinates format
                foreach ($gpsPointsCollection as $point) {
                    if (isset($point['latitude']) && isset($point['longitude'])) {
                        $fullGPSPoints[] = [
                            'lat' => floatval($point['latitude']),
                            'lng' => floatval($point['longitude']),
                            'timestamp' => isset($point['timestamp']) ? $point['timestamp'] : null,
                            'accuracy' => isset($point['accuracy']) ? $point['accuracy'] : null,
                            'speed' => isset($point['speed']) ? $point['speed'] : null,
                            'distance' => isset($point['distance']) ? $point['distance'] : null
                        ];
                    }
                }
                
                // Sort by timestamp if available
                if (!empty($fullGPSPoints) && isset($fullGPSPoints[0]['timestamp'])) {
                    usort($fullGPSPoints, function($a, $b) {
                        $timestampA = is_array($a['timestamp']) ? $a['timestamp']['seconds'] : strtotime($a['timestamp']);
                        $timestampB = is_array($b['timestamp']) ? $b['timestamp']['seconds'] : strtotime($b['timestamp']);
                        return $timestampA - $timestampB;
                    });
                }
                
                $analysis['statistics']['total_points'] = count($fullGPSPoints);
                $analysis['recommendations'][] = sprintf('Análise baseada em %d pontos GPS completos para máxima precisão', count($fullGPSPoints));
            }
        } catch (Exception $e) {
            // If GPS points query fails, fall back to trajectory coordinates
            error_log("Failed to query GPS points for session {$sessionId}: " . $e->getMessage());
        }
    }
    
    // If no full GPS points available, use trajectory coordinates (existing logic)
    $coordinates = [];
    if (!empty($fullGPSPoints)) {
        $coordinates = $fullGPSPoints;
    } else {
        // Get coordinates from various possible fields (existing logic)
        if (isset($trajeto['coordinates']) && is_array($trajeto['coordinates'])) {
            $coordinates = $trajeto['coordinates'];
        } elseif (isset($trajeto['route']) && is_array($trajeto['route'])) {
            $coordinates = $trajeto['route'];
        } elseif (isset($trajeto['pathCoordinates']) && is_array($trajeto['pathCoordinates'])) {
            $coordinates = $trajeto['pathCoordinates'];
        }
        
        $analysis['statistics']['total_points'] = count($coordinates);
        $analysis['recommendations'][] = 'Análise baseada em coordenadas de trajeto (limitada a 200 pontos)';
    }
    
    if (empty($coordinates)) {
        $analysis['issues'][] = 'Nenhuma coordenada encontrada no trajeto';
        return $analysis;
    }
    
    // Convert coordinates to standard format and validate
    $validCoordinates = [];
    foreach ($coordinates as $coord) {
        $lat = null;
        $lng = null;
        $timestamp = null;
        $accuracy = null;
        $speed = null;
        
        // Handle different coordinate formats
        if (is_array($coord)) {
            if (isset($coord['lat']) && isset($coord['lng'])) {
                $lat = floatval($coord['lat']);
                $lng = floatval($coord['lng']);
                $timestamp = $coord['timestamp'] ?? null;
                $accuracy = $coord['accuracy'] ?? null;
                $speed = $coord['speed'] ?? null;
            } elseif (isset($coord['latitude']) && isset($coord['longitude'])) {
                $lat = floatval($coord['latitude']);
                $lng = floatval($coord['longitude']);
                $timestamp = $coord['timestamp'] ?? null;
                $accuracy = $coord['accuracy'] ?? null;
                $speed = $coord['speed'] ?? null;
            } elseif (isset($coord[0]) && isset($coord[1])) {
                $lat = floatval($coord[0]);
                $lng = floatval($coord[1]);
                $timestamp = $coord[2] ?? null;
            }
        }
        
        // Validate coordinates (Portugal bounds approximately)
        if ($lat !== null && $lng !== null && 
            $lat >= 36.0 && $lat <= 42.5 && 
            $lng >= -9.5 && $lng <= -6.0) {
            $validCoordinates[] = [
                'lat' => $lat,
                'lng' => $lng,
                'timestamp' => $timestamp,
                'accuracy' => $accuracy,
                'speed' => $speed
            ];
        }
    }
    
    $analysis['statistics']['valid_points'] = count($validCoordinates);
    
    if (count($validCoordinates) < 2) {
        $analysis['issues'][] = 'Trajeto tem menos de 2 pontos válidos';
        return $analysis;
    }
    
    // Calculate distances and detect GPS jumps
    $totalDistance = 0;
    $maxSpeed = 0;
    $speeds = [];
    $gpsJumps = [];
    $speedViolations = [];
    $stationaryPoints = 0;
    
    for ($i = 1; $i < count($validCoordinates); $i++) {
        $prev = $validCoordinates[$i - 1];
        $curr = $validCoordinates[$i];
        
        // Calculate distance between points using Haversine formula
        $distance = calculateHaversineDistance($prev['lat'], $prev['lng'], $curr['lat'], $curr['lng']);
        $totalDistance += $distance;
        
        // Calculate speed if timestamps are available
        $timeDiff = null;
        if ($prev['timestamp'] && $curr['timestamp']) {
            // Handle different timestamp formats
            $prevTime = is_array($prev['timestamp']) ? $prev['timestamp']['seconds'] : strtotime($prev['timestamp']);
            $currTime = is_array($curr['timestamp']) ? $curr['timestamp']['seconds'] : strtotime($curr['timestamp']);
            $timeDiff = abs($currTime - $prevTime);
        }
        
        if ($timeDiff && $timeDiff > 0) {
            $speed = ($distance * 1000) / $timeDiff; // m/s
            $speedKmh = $speed * 3.6; // km/h
            $speeds[] = $speedKmh;
            $maxSpeed = max($maxSpeed, $speedKmh);
            
            // Detect GPS jumps (unrealistic speeds > 100 km/h for walking/slow movement)
            if ($speedKmh > 100) {
                $gpsJumps[] = [
                    'point_index' => $i,
                    'distance_m' => $distance * 1000,
                    'time_diff_s' => $timeDiff,
                    'speed_kmh' => round($speedKmh, 2),
                    'from' => ['lat' => $prev['lat'], 'lng' => $prev['lng']],
                    'to' => ['lat' => $curr['lat'], 'lng' => $curr['lng']],
                    'accuracy' => $curr['accuracy']
                ];
            }
            
            // Track speed violations (> 50 km/h might be unrealistic for hunting trajectories)
            if ($speedKmh > 50) {
                $speedViolations[] = [
                    'point_index' => $i,
                    'speed_kmh' => round($speedKmh, 2),
                    'distance_m' => $distance * 1000,
                    'time_diff_s' => $timeDiff,
                    'accuracy' => $curr['accuracy']
                ];
            }
            
            // Count stationary points (< 1 km/h)
            if ($speedKmh < 1) {
                $stationaryPoints++;
            }
        }
    }
    
    // Calculate time span
    $timeSpanMinutes = 0;
    if (count($validCoordinates) >= 2) {
        $firstPoint = $validCoordinates[0];
        $lastPoint = $validCoordinates[count($validCoordinates) - 1];
        
        if ($firstPoint['timestamp'] && $lastPoint['timestamp']) {
            $firstTime = is_array($firstPoint['timestamp']) ? $firstPoint['timestamp']['seconds'] : strtotime($firstPoint['timestamp']);
            $lastTime = is_array($lastPoint['timestamp']) ? $lastPoint['timestamp']['seconds'] : strtotime($lastPoint['timestamp']);
            $timeSpanMinutes = abs($lastTime - $firstTime) / 60;
        }
    }
    
    // Calculate average speed
    $avgSpeed = 0;
    if (!empty($speeds)) {
        $avgSpeed = array_sum($speeds) / count($speeds);
    }
    
    // Update statistics
    $analysis['statistics']['gps_jumps'] = count($gpsJumps);
    $analysis['statistics']['max_speed_kmh'] = round($maxSpeed, 2);
    $analysis['statistics']['avg_speed_kmh'] = round($avgSpeed, 2);
    $analysis['statistics']['total_distance_km'] = round($totalDistance, 2);
    $analysis['statistics']['recalculated_distance_km'] = round($totalDistance, 2);
    $analysis['statistics']['time_span_minutes'] = round($timeSpanMinutes, 2);
    $analysis['statistics']['stationary_points'] = $stationaryPoints;
    
    // Compare with stored distance if available
    $storedDistance = 0;
    if (isset($trajeto['totalDistance'])) {
        $storedDistance = floatval($trajeto['totalDistance']);
    } elseif (isset($trajeto['distance'])) {
        // Extract numeric value from distance string like "25.5 km"
        $distanceStr = $trajeto['distance'];
        if (preg_match('/([0-9.]+)/', $distanceStr, $matches)) {
            $storedDistance = floatval($matches[1]);
        }
    }
    
    if ($storedDistance > 0) {
        $difference = abs($totalDistance - $storedDistance);
        $analysis['statistics']['distance_difference_percent'] = round(($difference / $storedDistance) * 100, 2);
        $analysis['statistics']['distance_difference_km'] = round($difference, 2);
        
        // Add issue if there's a significant difference
        if ($difference > 5 && ($difference / $storedDistance) > 0.2) {
            $analysis['issues'][] = sprintf('Grande discrepância entre distância armazenada (%.2f km) e recalculada (%.2f km)', $storedDistance, $totalDistance);
            $analysis['recommendations'][] = 'A distância original pode estar inflada por saltos GPS ou cálculos incorretos';
        }
    } else {
        $analysis['statistics']['distance_difference_percent'] = 0;
        $analysis['statistics']['distance_difference_km'] = 0;
    }
    
    // Store detailed jump and speed violation data
    $analysis['gps_jumps'] = array_slice($gpsJumps, 0, 10); // Limit to first 10 jumps
    $analysis['speed_violations'] = array_slice($speedViolations, 0, 10); // Limit to first 10 violations
    
    // Analyze quality and generate issues/recommendations
    $qualityScore = 100;
    
    // ENHANCED: Better quality scoring based on data source
    if ($analysis['statistics']['analysis_source'] === 'full_gps_points') {
        $analysis['recommendations'][] = 'Análise de alta precisão com pontos GPS completos';
        
        // More accurate scoring with full GPS data
        if (count($gpsJumps) > 20) {
            $qualityScore = 0;
            $analysis['issues'][] = sprintf('MUITOS saltos GPS detectados (%d saltos >100 km/h) - DADOS CORROMPIDOS', count($gpsJumps));
            $analysis['recommendations'][] = 'ELIMINAR IMEDIATAMENTE - GPS gravemente corrompido';
        } elseif (count($gpsJumps) > 10) {
            $qualityScore -= 70;
            $analysis['issues'][] = sprintf('Muitos saltos GPS detectados (%d saltos >100 km/h)', count($gpsJumps));
            $analysis['recommendations'][] = 'Trajeto provavelmente corrompido - considere eliminação';
        } elseif (count($gpsJumps) > 5) {
            $qualityScore -= 40;
            $analysis['issues'][] = sprintf('Alguns saltos GPS detectados (%d saltos >100 km/h)', count($gpsJumps));
            $analysis['recommendations'][] = 'Trajeto com problemas GPS moderados';
        } elseif (count($gpsJumps) > 0) {
            $qualityScore -= 20;
            $analysis['issues'][] = sprintf('Poucos saltos GPS detectados (%d saltos >100 km/h)', count($gpsJumps));
        }
    } else {
        // Original scoring for limited trajectory coordinates
        if (count($gpsJumps) > 10) {
            $qualityScore = 0;
            $analysis['issues'][] = sprintf('MUITOS saltos GPS detectados (%d saltos >100 km/h) - DADOS CORROMPIDOS', count($gpsJumps));
            $analysis['recommendations'][] = 'ELIMINAR IMEDIATAMENTE - GPS gravemente corrompido';
        } elseif (count($gpsJumps) > 5) {
            $qualityScore -= 70;
            $analysis['issues'][] = sprintf('Muitos saltos GPS detectados (%d saltos >100 km/h)', count($gpsJumps));
            $analysis['recommendations'][] = 'Trajeto provavelmente corrompido - considere eliminação';
        }
    }
    
    // Deduct points for unrealistic distances - BE MORE STRICT
    if ($totalDistance > 100) {
        $qualityScore = 0; // Automatic fail for impossible distances
        $analysis['issues'][] = sprintf('Distância impossível detectada (%.2f km) - TRAJETO INVÁLIDO', $totalDistance);
        $analysis['recommendations'][] = 'ELIMINAR IMEDIATAMENTE - Distância fisicamente impossível para monitorização a pé';
    } elseif ($totalDistance > 50) {
        $qualityScore -= 80; // Heavy penalty for very unlikely distances
        $analysis['issues'][] = sprintf('Distância muito alta (%.2f km) - provavelmente erro GPS', $totalDistance);
        $analysis['recommendations'][] = 'Verificar se trajeto é válido - distância muito alta para monitorização típica';
    } elseif ($totalDistance > 25) {
        $qualityScore -= 50; // Moderate penalty for high distances
        $analysis['issues'][] = sprintf('Distância alta (%.2f km) - verificar se é realista', $totalDistance);
        $analysis['recommendations'][] = 'Distância elevada - confirmar se corresponde à atividade real';
    } elseif ($totalDistance > 15) {
        $qualityScore -= 20; // Small penalty for moderately high distances
        $analysis['issues'][] = sprintf('Distância moderadamente alta (%.2f km)', $totalDistance);
    }
    
    // Deduct points for speed violations
    if (count($speedViolations) > 20) {
        $qualityScore -= 40;
        $analysis['issues'][] = sprintf('Muitas violações de velocidade detectadas (%d violações >50 km/h)', count($speedViolations));
        $analysis['recommendations'][] = 'GPS instável - muitas velocidades irreais detectadas';
    } elseif (count($speedViolations) > 10) {
        $qualityScore -= 20;
        $analysis['issues'][] = sprintf('Algumas violações de velocidade detectadas (%d violações >50 km/h)', count($speedViolations));
    }
    
    // Deduct points for high percentage of stationary points (might indicate GPS issues)
    if ($stationaryPoints > 0 && count($validCoordinates) > 10) {
        $stationaryPercentage = ($stationaryPoints / count($validCoordinates)) * 100;
        if ($stationaryPercentage > 80) {
            $qualityScore -= 30;
            $analysis['issues'][] = sprintf('Muitos pontos estacionários (%.1f%%) - possível problema GPS', $stationaryPercentage);
            $analysis['recommendations'][] = 'GPS pode ter tido dificuldades em obter sinal de qualidade';
        }
    }
    
    // Ensure quality score doesn't go below 0
    $qualityScore = max(0, $qualityScore);
    
    // Determine quality rating
    if ($qualityScore == 0) {
        $analysis['quality_rating'] = 'INVÁLIDO';
    } elseif ($qualityScore >= 80) {
        $analysis['quality_rating'] = 'Excelente';
    } elseif ($qualityScore >= 60) {
        $analysis['quality_rating'] = 'Boa';
    } elseif ($qualityScore >= 40) {
        $analysis['quality_rating'] = 'Razoável';
    } elseif ($qualityScore >= 20) {
        $analysis['quality_rating'] = 'Má';
    } else {
        $analysis['quality_rating'] = 'Muito Má';
    }
    
    $analysis['quality_score'] = $qualityScore;
    
    // Add final recommendations based on quality score
    if ($qualityScore == 0) {
        $analysis['recommendations'][] = 'AÇÃO REQUERIDA: Eliminar este trajeto da base de dados';
    } elseif ($qualityScore < 40) {
        $analysis['recommendations'][] = 'RECOMENDAÇÃO: Considerar eliminação deste trajeto';
    } elseif ($qualityScore < 60) {
        $analysis['recommendations'][] = 'AVISO: Trajeto com qualidade questionável';
    } else {
        $analysis['recommendations'][] = 'Trajeto com qualidade aceitável';
    }
    
    return $analysis;
}

// Haversine distance calculation
function calculateHaversineDistance($lat1, $lng1, $lat2, $lng2) {
    $earthRadius = 6371; // km
    
    $dLat = deg2rad($lat2 - $lat1);
    $dLng = deg2rad($lng2 - $lng1);
    
    $a = sin($dLat / 2) * sin($dLat / 2) +
         cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
         sin($dLng / 2) * sin($dLng / 2);
    
    $c = 2 * atan2(sqrt($a), sqrt(1 - $a));
    
    return $earthRadius * $c;
}

// PERFORMANCE OPTIMIZATION: Enhanced user lookup function with improved caching
function lookupUserInfo($userId, $firebase) {
    if (empty($userId)) return null;
    
    $userInfo = null;
    
    try {
        // First, try to get from Firestore collections
        // Check users collection
        try {
            $userData = $firebase->getDocument('users', $userId);
            if ($userData && isset($userData['email'])) {
                $userInfo = [
                    'email' => $userData['email'],
                    'name' => $userData['name'] ?? null,
                    'source' => 'users'
                ];
            }
        } catch (Exception $e) {
            // User not found in users collection, try gestoresZonaCaca
            try {
                $gestorData = $firebase->getDocument('gestoresZonaCaca', $userId);
                if ($gestorData && isset($gestorData['email'])) {
                    $userInfo = [
                        'email' => $gestorData['email'],
                        'name' => $gestorData['name'] ?? null,
                        'source' => 'gestoresZonaCaca'
                    ];
                }
            } catch (Exception $e2) {
                // Not found in Firestore collections, try Firebase Auth
                try {
                    $email = $firebase->getFirebaseAuthEmail($userId);
                    if ($email) {
                        $userInfo = [
                            'email' => $email,
                            'name' => null,
                            'source' => 'firebase_auth'
                        ];
                    }
                } catch (Exception $e3) {
                    // Could not find user anywhere
                    $userInfo = null;
                }
            }
        }
        
    } catch (Exception $e) {
        error_log("Error looking up user $userId: " . $e->getMessage());
        $userInfo = null;
    }
    
    return $userInfo;
}
?>
<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SITE_NAME; ?> - Trajetos das Zonas de Caça</title>
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="<?php echo SITE_URL; ?>/assets/css/style.css?v=<?php echo time(); ?>" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    
    <style>
        .content {
            padding-top: 5rem;
            padding-left: 0;
            padding-right: 0;
            padding-bottom: 0;
        }

        .page-header {
            background: white;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .page-header h1 {
            margin: 0;
            font-size: 1.5rem;
            color: #374151;
            font-weight: 500;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 1rem;
            margin: 0 1.5rem 1.5rem 1.5rem;
            overflow-x: auto;
            padding-bottom: 0.5rem;
        }

        /* Responsive stats grid */
        @media (max-width: 1200px) {
            .stats-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        @media (max-width: 900px) {
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .stats-grid {
                display: flex;
                flex-wrap: nowrap;
                overflow-x: auto;
                scroll-behavior: smooth;
                -webkit-overflow-scrolling: touch;
                scrollbar-width: thin;
                scrollbar-color: #cbd5e1 #f1f5f9;
                margin: 0 1rem 1.5rem 1rem;
                padding: 0 0.5rem 0.5rem 0.5rem;
            }

            .stats-grid::-webkit-scrollbar {
                height: 6px;
            }

            .stats-grid::-webkit-scrollbar-track {
                background: #f1f5f9;
                border-radius: 3px;
            }

            .stats-grid::-webkit-scrollbar-thumb {
                background: #cbd5e1;
                border-radius: 3px;
            }

            .stats-grid::-webkit-scrollbar-thumb:hover {
                background: #94a3b8;
            }
        }

        @media (max-width: 480px) {
            .stats-grid {
                margin: 0 0.5rem 1.5rem 0.5rem;
                padding: 0 0.25rem 0.5rem 0.25rem;
            }
        }

        .stat-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 1rem;
            display: flex;
            align-items: center;
            gap: 1rem;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        /* Responsive stat card adjustments */
        @media (max-width: 768px) {
            .stat-card {
                min-width: 280px;
                flex-shrink: 0;
                padding: 1.25rem;
            }
        }

        @media (max-width: 480px) {
            .stat-card {
                min-width: 260px;
                padding: 1rem;
            }
            
            .stat-card .icon {
                width: 36px;
                height: 36px;
            }
            
            .stat-card .icon i {
                font-size: 16px;
            }
            
            .stat-card .title {
                font-size: 0.8rem;
            }
            
            .stat-card .value {
                font-size: 1.25rem;
            }
        }

        .stat-card:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .stat-card .icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .stat-card .icon i {
            font-size: 18px;
            color: white;
        }

        .stat-content {
            flex: 1;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .stat-card .title {
            font-size: 0.875rem;
            color: #6B7280;
            font-weight: 600;
            margin: 0;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }

        .stat-card .value {
            font-size: 1.5rem;
            font-weight: 700;
            color: #374151;
            margin: 0;
        }

        .bg-blue {
            background-color: #3B82F6;
        }

        .bg-purple {
            background-color: #8B5CF6;
        }

        .bg-green {
            background-color: #10B981;
        }

        .bg-orange {
            background-color: #F59E0B;
        }

        .badge {
            display: inline-flex;
            align-items: center;
            padding: 0.375rem 0.75rem;
            font-size: 0.75rem;
            font-weight: 600;
            border-radius: 0.375rem;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }

        .badge-primary {
            background-color: #3b82f6;
            color: white;
        }

        .badge-success {
            background-color: #10b981;
            color: white;
        }

        .badge-secondary {
            background-color: #6b7280;
            color: white;
        }

        .badge-info {
            background-color: #0ea5e9;
            color: white;
        }

        /* Table card styling */
        .zones-table-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin: 0 1.5rem 1.5rem 1.5rem;
            overflow: hidden;
        }

        .zones-table-header {
            padding: 1.5rem;
            border-bottom: 1px solid #E5E7EB;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .zones-table-header h2 {
            margin: 0;
            font-size: 1.125rem;
            color: #374151;
            font-weight: 500;
        }

        .zones-table-body {
            padding: 0 1.5rem 1.5rem 1.5rem;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }

        /* DataTable responsive wrapper */
        .zones-table-body table {
            min-width: 1000px; /* Reduced from 1200px since we removed 2 columns */
        }

        /* DataTables layout adjustments - move search to right */
        .dataTables_wrapper .dataTables_length,
        .dataTables_wrapper .dataTables_filter {
            display: inline-block;
            float: none;
            margin-bottom: 1rem;
        }

        .dataTables_wrapper .dataTables_filter {
            float: right;
            text-align: right;
        }

        .dataTables_wrapper .dataTables_length {
            float: left;
        }

        .dataTables_wrapper .dataTables_length select,
        .dataTables_wrapper .dataTables_filter input {
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
        }

        .dataTables_wrapper .dataTables_filter input {
            margin-left: 0.5rem;
            width: 200px;
        }

        .dataTables_wrapper .dataTables_length label,
        .dataTables_wrapper .dataTables_filter label {
            font-weight: 500;
            color: #374151;
            margin-bottom: 0;
        }

        .dataTables_wrapper .dataTables_filter label {
            display: flex;
            align-items: center;
            justify-content: flex-end;
        }

        .dataTables_wrapper {
            margin-top: 0;
        }

        .dataTables_wrapper .dataTables_length,
        .dataTables_wrapper .dataTables_filter,
        .dataTables_wrapper .dataTables_info,
        .dataTables_wrapper .dataTables_paginate {
            padding: 0.5rem 0;
        }

        /* Custom scrollbar for table */
        .zones-table-body::-webkit-scrollbar {
            height: 8px;
        }

        .zones-table-body::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 4px;
        }

        .zones-table-body::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 4px;
        }

        .zones-table-body::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* Firefox scrollbar */
        .zones-table-body {
            scrollbar-width: thin;
            scrollbar-color: #cbd5e1 #f1f5f9;
        }

        /* Responsive table adjustments */
        @media (max-width: 768px) {
            .zones-table-card {
                margin: 0 1rem 1.5rem 1rem;
            }
            
            .zones-table-header {
                padding: 1rem;
                flex-direction: column;
                gap: 1rem;
                align-items: stretch;
            }
            
            .zones-table-header h2 {
                text-align: center;
            }
            
            .zones-table-body {
                padding: 0 1rem 1rem 1rem;
            }
        }

        @media (max-width: 480px) {
            .zones-table-card {
                margin: 0 0.5rem 1.5rem 0.5rem;
            }
            
            .zones-table-header {
                padding: 0.75rem;
            }
            
            .zones-table-body {
                padding: 0 0.75rem 0.75rem 0.75rem;
            }
        }

        .text-center {
            text-align: center !important;
        }

        /* Prevent line breaking in table cells */
        table td {
            white-space: nowrap;
        }

        table td:nth-child(1) {
            white-space: normal; /* Allow wrapping for trajeto name */
            min-width: 200px;
        }

        .text-muted {
            color: #9ca3af !important;
            font-size: 0.875rem;
        }

        .me-2 {
            margin-right: 0.5rem !important;
        }

        .alert {
            padding: 0.75rem 1rem;
            border-radius: 6px;
            border: 1px solid transparent;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
            margin: 0 1.5rem 1rem 1.5rem;
        }

        .alert-danger {
            background-color: #fef2f2;
            border-color: #fecaca;
            color: #dc2626;
        }

        .alert-success {
            background-color: #f0fdf4;
            border-color: #bbf7d0;
            color: #166534;
        }

        .alert-danger i,
        .alert-success i {
            color: inherit;
        }

        /* Modal styling */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(4px);
        }

        .modal-dialog {
            position: relative;
            width: auto;
            margin: 1.75rem auto;
            max-width: 500px;
        }

        .modal-content {
            position: relative;
            display: flex;
            flex-direction: column;
            width: 100%;
            background-color: #fff;
            background-clip: padding-box;
            border: 1px solid rgba(0, 0, 0, 0.2);
            border-radius: 12px;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        .modal-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #e5e7eb;
            border-top-left-radius: 12px;
            border-top-right-radius: 12px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }

        .modal-header h5 {
            margin: 0;
            font-size: 1.125rem;
            font-weight: 600;
            color: #374151;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .modal-body {
            position: relative;
            flex: 1 1 auto;
            padding: 1.5rem;
        }

        .modal-footer {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding: 1rem 1.5rem;
            border-top: 1px solid #e5e7eb;
            border-bottom-right-radius: 12px;
            border-bottom-left-radius: 12px;
            background: #f8fafc;
            gap: 0.75rem;
        }

        .close {
            background: none;
            border: none;
            font-size: 1.25rem;
            color: #6b7280;
            cursor: pointer;
            padding: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: color 0.2s ease;
        }

        .close:hover {
            color: #374151;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            font-weight: 500;
            border-radius: 6px;
            border: 1px solid transparent;
            cursor: pointer;
            text-decoration: none;
            transition: all 0.2s ease;
        }

        .btn-sm {
            padding: 0.375rem 0.75rem;
            font-size: 0.8125rem;
        }

        .btn-primary {
            background-color: #3b82f6;
            border-color: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background-color: #2563eb;
            border-color: #2563eb;
        }

        .btn-secondary {
            background-color: #6b7280;
            border-color: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #4b5563;
            border-color: #4b5563;
        }

        .btn-danger {
            background-color: #ef4444;
            border-color: #ef4444;
            color: white;
        }

        .btn-danger:hover {
            background-color: #dc2626;
            border-color: #dc2626;
        }

        .btn-info {
            background-color: #06b6d4;
            border-color: #06b6d4;
            color: white;
        }

        .btn-info:hover {
            background-color: #0891b2;
            border-color: #0891b2;
        }

        .badge {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
            font-weight: 500;
            border-radius: 12px;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }

        .badge-secondary {
            background-color: #f3f4f6;
            color: #374151;
        }

        .badge-info {
            background-color: #dbeafe;
            color: #1e40af;
        }

        .badge-primary {
            background-color: #dbeafe;
            color: #1e40af;
        }

        .empty-state {
            text-align: center;
            padding: 3rem 1rem;
        }

        .empty-state i {
            font-size: 3rem;
            color: #9ca3af;
            margin-bottom: 1rem;
        }

        .empty-state h5 {
            color: #6b7280;
            margin-bottom: 0.5rem;
        }

        .empty-state p {
            color: #9ca3af;
            margin: 0;
        }

        /* Simple loading indicator for better UX */
        .page-loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #3b82f6 0%, #10b981 50%, #3b82f6 100%);
            background-size: 200% 100%;
            animation: loading-bar 2s ease-in-out infinite;
            z-index: 9999;
            opacity: 1;
            transition: opacity 0.3s ease;
        }

        .page-loading.hidden {
            opacity: 0;
        }

        @keyframes loading-bar {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }


    </style>
</head>
<body>
    <!-- Simple loading bar -->
    <div class="page-loading" id="pageLoading"></div>



    <?php 
    include_file('includes/header.php');
    include_file('includes/sidebar.php');
    ?>
    
    <div class="content">
        <!-- Alerts -->
        <?php if (!empty($error)): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i>
                <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>

        <?php if (!empty($success)): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                <?php echo htmlspecialchars($success); ?>
            </div>
        <?php endif; ?>



        <!-- Statistics Grid -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="icon bg-blue">
                    <i class="fas fa-route"></i>
                </div>
                <div class="stat-content">
                    <p class="title">Total de Trajetos</p>
                    <p class="value"><?php echo count($trajetos); ?></p>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="icon bg-green">
                    <i class="fas fa-mobile-alt"></i>
                </div>
                <div class="stat-content">
                    <p class="title">GPS</p>
                    <p class="value"><?php echo count(array_filter($trajetos, function($t) { return ($t['type'] ?? '') === 'GPS'; })); ?></p>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="icon bg-indigo" style="background-color: #6366f1;">
                    <i class="fas fa-pencil-alt"></i>
                </div>
                <div class="stat-content">
                    <p class="title">Manual</p>
                    <p class="value"><?php echo count(array_filter($trajetos, function($t) { return ($t['type'] ?? '') === 'Manual'; })); ?></p>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="icon bg-orange">
                    <i class="fas fa-route"></i>
                </div>
                <div class="stat-content">
                    <p class="title">Total KMs</p>
                    <p class="value"><?php 
                        $totalKms = 0;
                        foreach ($trajetos as $trajeto) {
                            if (isset($trajeto['distance']) && !empty($trajeto['distance'])) {
                                // Extract numeric value from distance string (e.g., "3.47 KM" -> 3.47)
                                $distance = preg_replace('/[^0-9.,]/', '', $trajeto['distance']);
                                $distance = str_replace(',', '.', $distance);
                                if (is_numeric($distance)) {
                                    $totalKms += floatval($distance);
                                }
                            }
                        }
                        echo number_format($totalKms, 1);
                    ?></p>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="icon bg-purple">
                    <i class="fas fa-dove"></i>
                </div>
                <div class="stat-content">
                    <p class="title">Contactos</p>
                    <p class="value"><?php 
                        $totalContacts = 0;
                        foreach ($trajetos as $trajeto) {
                            if (isset($trajeto['contacts']) && is_array($trajeto['contacts'])) {
                                $totalContacts += count($trajeto['contacts']);
                            }
                        }
                        echo $totalContacts;
                    ?></p>
                </div>
            </div>
        </div>



        <!-- Trajetos Table -->
        <div class="zones-table-card">
            <div class="zones-table-header">
                <h2>
                    <i class="fas fa-list-alt" style="color: var(--primary-color); margin-right: 0.5rem;"></i>
                    Lista de Trajetos
                </h2>
                <div style="display: flex; gap: 1rem; align-items: center;">
                    <a href="scan_gps_problems.php" class="btn btn-warning" style="text-decoration: none; padding: 0.5rem 1rem; border-radius: 6px; font-size: 0.875rem;">
                        <i class="fas fa-search"></i>
                        Analisar Problemas GPS
                    </a>
                </div>
            </div>
            <div class="zones-table-body">
                <?php if (empty($trajetos)): ?>
                    <div class="empty-state">
                        <i class="fas fa-route"></i>
                        <h5>Nenhum trajeto encontrado</h5>
                        <p>Não existem trajetos criados pelos gestores das zonas de caça.</p>
                    </div>
                <?php else: ?>
                    <table id="trajetos-table" class="table">
                        <thead>
                            <tr>
                                <th>Nome</th>
                                <th class="text-center">Document ID</th>
                                <th class="text-center">Tipo</th>
                                <th class="text-center">Criado por</th>
                                <th class="text-center">Data</th>
                                <th class="text-center">Distância</th>
                                <th class="text-center">Pontos</th>
                                <th class="text-center">Contactos</th>
                                <th class="text-center">Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($trajetos as $index => $trajeto): ?>
                            <tr data-trajeto-id="<?php echo htmlspecialchars($trajeto['id'] ?? 'N/A'); ?>" data-row-index="<?php echo $index; ?>">
                                <td>
                                    <strong style="color:#484848"><?php
                                        $name = $trajeto['name'] ?? 'Trajeto sem nome';
                                        // Remove date from trajectory name (everything after " - ")
                                        $cleanName = preg_replace('/ - \d{2}\/\d{2}\/\d{4} \d{2}:\d{2}$/', '', $name);
                                        echo htmlspecialchars($cleanName);
                                    ?></strong>
                                </td>
                                <td class="text-center">
                                    <code style="font-size: 0.75rem; background-color: #f3f4f6; padding: 0.25rem 0.5rem; border-radius: 4px; color: #374151;"><?php echo htmlspecialchars($trajeto['id'] ?? 'N/A'); ?></code>
                                </td>
                                <td class="text-center">
                                    <?php if (($trajeto['type'] ?? '') === 'GPS'): ?>
                                        <span class="badge badge-success" style="background-color: #10b981; color: white;">
                                            <i class="fas fa-mobile-alt" style="margin-right: 0.25rem;"></i>GPS
                                        </span>
                                    <?php else: ?>
                                        <span class="badge badge-primary" style="background-color: #6366f1; color: white;">
                                            <i class="fas fa-pencil-alt" style="margin-right: 0.25rem;"></i>Manual
                                        </span>
                                    <?php endif; ?>
                                </td>
                                <td class="text-center">
                                    <small><?php 
                                        if (!empty($trajeto['createdByEmail'])) {
                                            echo htmlspecialchars($trajeto['createdByEmail']);
                                        } elseif (!empty($trajeto['createdBy'])) {
                                            // Check if it looks like a Firebase UID (alphanumeric, 20+ chars)
                                            if (preg_match('/^[a-zA-Z0-9]{20,}$/', $trajeto['createdBy'])) {
                                                echo '<span style="color: #dc2626; font-style: italic;" title="Firebase UID: ' . htmlspecialchars($trajeto['createdBy']) . '">⚠️ User not found</span>';
                                            } else {
                                                echo htmlspecialchars($trajeto['createdBy']);
                                            }
                                        } else {
                                            echo 'N/A';
                                        }
                                    ?></small>
                                </td>
                                <td class="text-center" data-sort="<?php echo getTimestamp($trajeto['createdAt'] ?? ''); ?>">
                                    <small><?php echo formatDate($trajeto['createdAt'] ?? ''); ?></small>
                                </td>
                                <td class="text-center" data-sort="<?php 
                                    $distance = $trajeto['distance'] ?? '0 km';
                                    // Extract numeric value from distance string (e.g., "162.65 km" -> 162.65)
                                    $numericDistance = 0;
                                    if (preg_match('/([0-9]+(?:\.[0-9]+)?)\s*km/', $distance, $matches)) {
                                        $numericDistance = floatval($matches[1]);
                                    }
                                    echo $numericDistance;
                                ?>">
                                    <span class="badge badge-secondary"><?php echo htmlspecialchars($distance); ?></span>
                                </td>
                                <td class="text-center">
                                    <span class="badge badge-info"><?php echo count($trajeto['coordinates'] ?? []); ?></span>
                                </td>
                                <td class="text-center">
                                    <span class="badge badge-primary"><?php echo $trajeto['contactCount'] ?? 0; ?></span>
                                </td>
                                <td class="text-center">
                                    <a href="../map/?type=trajetos&search=<?php echo urlencode($trajeto['id']); ?>" 
                                       class="btn btn-sm btn-primary me-2" 
                                       title="Ver no Mapa">
                                        <i class="fas fa-map-marked-alt"></i>
                                        Mapa
                                    </a>
                                    <button type="button" 
                                            class="btn btn-sm btn-info me-2" 
                                            onclick="viewTrajeto('<?php echo htmlspecialchars($trajeto['id']); ?>')"
                                            title="Ver Detalhes">
                                        <i class="fas fa-eye"></i>
                                        Ver
                                    </button>
                                    <?php if (($trajeto['type'] ?? '') === 'GPS'): ?>
                                    <button type="button" 
                                            class="btn btn-sm btn-warning me-2" 
                                            onclick="analyzeGPSTrajectory('<?php echo htmlspecialchars($trajeto['id']); ?>')"
                                            title="Analisar Qualidade GPS">
                                        <i class="fas fa-chart-line"></i>
                                        Analisar
                                    </button>
                                    <?php endif; ?>
                                    <button type="button" 
                                            class="btn btn-sm btn-danger" 
                                            onclick="deleteTrajeto('<?php echo htmlspecialchars($trajeto['id']); ?>', '<?php echo htmlspecialchars($trajeto['name'] ?? 'Trajeto sem nome'); ?>')"
                                            title="Eliminar">
                                        <i class="fas fa-trash"></i>
                                        Eliminar
                                    </button>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal" id="deleteModal">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5>
                        <i class="fas fa-exclamation-triangle" style="color: #f59e0b;"></i>
                        Confirmar Eliminação
                    </h5>
                    <button type="button" class="close" onclick="closeDeleteModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <p>Tem a certeza que pretende eliminar o trajeto <strong id="deleteTrajetoName"></strong>?</p>
                    <p style="color: #dc2626;">
                        <i class="fas fa-exclamation-circle" style="margin-right: 0.5rem;"></i>
                        Esta ação não pode ser desfeita. Todos os dados associados ao trajeto, incluindo contactos, serão permanentemente eliminados.
                    </p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeDeleteModal()">Cancelar</button>
                    <form method="POST" style="display: inline;" onsubmit="showDeleteLoading()">
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" name="trajeto_id" id="deleteTrajetoId">
                        <button type="submit" class="btn btn-danger" id="deleteSubmitBtn">
                            <i class="fas fa-trash" id="deleteIcon"></i>
                            <span id="deleteText">Eliminar</span>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Trajeto Details Modal -->
    <div class="modal" id="trajetoModal">
        <div class="modal-dialog" style="max-width: 800px;">
            <div class="modal-content">
                <div class="modal-header">
                    <h5>
                        <i class="fas fa-route"></i>
                        Detalhes do Trajeto
                    </h5>
                    <button type="button" class="close" onclick="closeTrajetoModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body" id="trajetoDetails">
                    <div style="text-align: center;">
                        <div style="display: inline-block; width: 32px; height: 32px; border: 3px solid #f3f4f6; border-radius: 50%; border-top-color: #3b82f6; animation: spin 1s linear infinite;"></div>
                        <p style="margin-top: 1rem; color: #6b7280;">A carregar...</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeTrajetoModal()">Fechar</button>
                    <a href="#" id="viewOnMapBtn" class="btn btn-primary">
                        <i class="fas fa-map-marked-alt"></i>
                        Ver no Mapa
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- GPS Analysis Modal -->
    <div class="modal" id="gpsAnalysisModal">
        <div class="modal-dialog" style="max-width: 900px;">
            <div class="modal-content">
                <div class="modal-header">
                    <h5>
                        <i class="fas fa-chart-line" style="color: #f59e0b;"></i>
                        Análise de Qualidade GPS
                    </h5>
                    <button type="button" class="close" onclick="closeGPSAnalysisModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body" id="gpsAnalysisContent">
                    <div style="text-align: center;">
                        <div style="display: inline-block; width: 32px; height: 32px; border: 3px solid #f3f4f6; border-radius: 50%; border-top-color: #f59e0b; animation: spin 1s linear infinite;"></div>
                        <p style="margin-top: 1rem; color: #6b7280;">A analisar trajeto GPS...</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeGPSAnalysisModal()">Fechar</button>
                </div>
            </div>
        </div>
    </div>

    <script>

        
        $(document).ready(function() {
            <?php if (!empty($trajetos)): ?>
            
            // Destroy existing DataTable if it exists
            if ($.fn.DataTable.isDataTable('#trajetos-table')) {
                $('#trajetos-table').DataTable().destroy();
            }
            
            var table = $('#trajetos-table').DataTable({
                language: {
                    url: 'https://cdn.datatables.net/plug-ins/1.13.6/i18n/pt-PT.json'
                },
                order: [[4, 'desc']], // Sort by date descending
                pageLength: 25,
                responsive: true,
                columnDefs: [
                    { targets: [1, 2, 3, 4, 5, 6, 7, 8], className: 'text-center' },
                    { 
                        targets: 4, // Date column
                        type: 'num' // Treat as numeric for sorting (will use data-sort attribute)
                    },
                    { 
                        targets: 5, // Distance column
                        type: 'num' // Treat as numeric for sorting (will use data-sort attribute)
                    }
                ],
                destroy: true,
                initComplete: function() {
                    hideLoadingOverlay();
                }
            });
            
            <?php else: ?>
            hideLoadingOverlay();
            <?php endif; ?>
        });

        function hideLoadingOverlay() {
            // Hide loading bar
            $('#pageLoading').addClass('hidden');
        }

        // Update loading progress for long operations
        function updateLoadingProgress(message) {
            $('#loadingProgress').text(message);
        }

        // Delete trajeto function
        function deleteTrajeto(trajetoId, trajetoName) {
            const deleteModal = document.getElementById('deleteModal');
            const deleteTrajetoId = document.getElementById('deleteTrajetoId');
            const deleteTrajetoName = document.getElementById('deleteTrajetoName');
            
            if (deleteTrajetoId) {
                deleteTrajetoId.value = trajetoId;
            }
            
            if (deleteTrajetoName) {
                deleteTrajetoName.textContent = trajetoName;
            }
            
            if (deleteModal) {
                deleteModal.style.display = 'block';
            }
        }

        function closeDeleteModal() {
            const deleteModal = document.getElementById('deleteModal');
            if (deleteModal) {
                deleteModal.style.display = 'none';
            }
            // Reset delete button state when modal closes
            resetDeleteButton();
        }

        function showDeleteLoading() {
            const deleteBtn = document.getElementById('deleteSubmitBtn');
            const deleteIcon = document.getElementById('deleteIcon');
            const deleteText = document.getElementById('deleteText');
            
            if (deleteBtn && deleteIcon && deleteText) {
                // Disable the button to prevent double clicks
                deleteBtn.disabled = true;
                deleteBtn.style.opacity = '0.7';
                deleteBtn.style.cursor = 'not-allowed';
                
                // Change icon to spinner
                deleteIcon.className = 'fas fa-spinner fa-spin';
                deleteText.textContent = 'A eliminar...';
            }
        }

        function resetDeleteButton() {
            const deleteBtn = document.getElementById('deleteSubmitBtn');
            const deleteIcon = document.getElementById('deleteIcon');
            const deleteText = document.getElementById('deleteText');
            
            if (deleteBtn && deleteIcon && deleteText) {
                // Re-enable the button
                deleteBtn.disabled = false;
                deleteBtn.style.opacity = '1';
                deleteBtn.style.cursor = 'pointer';
                
                // Reset icon and text
                deleteIcon.className = 'fas fa-trash';
                deleteText.textContent = 'Eliminar';
            }
        }

        // View trajeto details function
        function viewTrajeto(trajetoId) {
            const modal = document.getElementById('trajetoModal');
            const detailsContainer = document.getElementById('trajetoDetails');
            const viewOnMapBtn = document.getElementById('viewOnMapBtn');
            
            // Show loading
            detailsContainer.innerHTML = `
                <div style="text-align: center;">
                    <div style="display: inline-block; width: 32px; height: 32px; border: 3px solid #f3f4f6; border-radius: 50%; border-top-color: #3b82f6; animation: spin 1s linear infinite;"></div>
                    <p style="margin-top: 1rem; color: #6b7280;">A carregar...</p>
                </div>
            `;
            
            // Set map link
            viewOnMapBtn.href = `../map/?type=trajetos&search=${encodeURIComponent(trajetoId)}`;
            
            modal.style.display = 'block';
            
            // Load trajeto details
            setTimeout(() => {
                loadTrajetoDetails(trajetoId, detailsContainer);
            }, 500);
        }

        function closeTrajetoModal() {
            document.getElementById('trajetoModal').style.display = 'none';
        }

        function loadTrajetoDetails(trajetoId, container) {
            // Find trajeto data from the current page
            const trajetos = <?php echo json_encode($trajetos); ?>;
            const trajeto = trajetos.find(t => t.id === trajetoId);
            
            if (!trajeto) {
                container.innerHTML = '<div style="color: #dc2626; text-align: center;">Trajeto não encontrado.</div>';
                return;
            }
            
            const html = `
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
                    <div>
                        <h6 style="color: #374151; font-weight: 600; margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fas fa-info-circle" style="color: #3b82f6;"></i>
                            Informações Gerais
                        </h6>
                        <table style="width: 100%; font-size: 0.875rem;">
                            <tr><td style="padding: 0.25rem 0; font-weight: 600;">Nome:</td><td style="padding: 0.25rem 0;">${trajeto.name || 'N/A'}</td></tr>
                            <tr><td style="padding: 0.25rem 0; font-weight: 600;">Tipo:</td><td style="padding: 0.25rem 0;">
                                ${trajeto.type === 'GPS' ? 
                                    '<span style="background-color: #10b981; color: white; padding: 0.25rem 0.5rem; border-radius: 0.25rem; font-size: 0.75rem; font-weight: 600;"><i class="fas fa-mobile-alt" style="margin-right: 0.25rem;"></i>GPS</span>' : 
                                    '<span style="background-color: #6366f1; color: white; padding: 0.25rem 0.5rem; border-radius: 0.25rem; font-size: 0.75rem; font-weight: 600;"><i class="fas fa-pencil-alt" style="margin-right: 0.25rem;"></i>Manual</span>'
                                }
                            </td></tr>
                            <tr><td style="padding: 0.25rem 0; font-weight: 600;">Descrição:</td><td style="padding: 0.25rem 0;">${trajeto.description || 'N/A'}</td></tr>
                            <tr><td style="padding: 0.25rem 0; font-weight: 600;">Distância:</td><td style="padding: 0.25rem 0;">${trajeto.distance || 'N/A'}</td></tr>
                            <tr><td style="padding: 0.25rem 0; font-weight: 600;">Pontos:</td><td style="padding: 0.25rem 0;">${(trajeto.coordinates || []).length}</td></tr>
                        </table>
                    </div>
                    <div>
                        <h6 style="color: #374151; font-weight: 600; margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fas fa-user" style="color: #3b82f6;"></i>
                            Criação
                        </h6>
                        <table style="width: 100%; font-size: 0.875rem;">
                            <tr><td style="padding: 0.25rem 0; font-weight: 600;">Criado por:</td><td style="padding: 0.25rem 0;">${trajeto.createdByEmail || trajeto.createdBy || 'N/A'}</td></tr>
                            <tr><td style="padding: 0.25rem 0; font-weight: 600;">Data:</td><td style="padding: 0.25rem 0;">${formatDate(trajeto.createdAt || '')}</td></tr>
                            <tr><td style="padding: 0.25rem 0; font-weight: 600;">Data do Trajeto:</td><td style="padding: 0.25rem 0;">${trajeto.date || 'N/A'}</td></tr>
                            <tr><td style="padding: 0.25rem 0; font-weight: 600;">Hora de Início:</td><td style="padding: 0.25rem 0;">${trajeto.startTime || 'N/A'}</td></tr>
                            <tr><td style="padding: 0.25rem 0; font-weight: 600;">Observadores:</td><td style="padding: 0.25rem 0;">${trajeto.numberOfObservers || 'N/A'}</td></tr>
                            <tr><td style="padding: 0.25rem 0; font-weight: 600;">Condições:</td><td style="padding: 0.25rem 0;">${trajeto.weatherCondition || 'N/A'}</td></tr>
                        </table>
                    </div>
                </div>
                ${(trajeto.contacts && trajeto.contacts.length > 0) ? `
                <div style="margin-top: 2rem;">
                    <h6 style="color: #374151; font-weight: 600; margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem;">
                        <i class="fas fa-dove" style="color: #3b82f6;"></i>
                        Contactos (${trajeto.contacts.length})
                    </h6>
                    <div style="overflow-x: auto;">
                        <table style="width: 100%; font-size: 0.875rem; border-collapse: collapse;">
                            <thead>
                                <tr style="background-color: #f9fafb;">
                                    <th style="padding: 0.5rem; text-align: left; border-bottom: 1px solid #e5e7eb;">Hora</th>
                                    <th style="padding: 0.5rem; text-align: left; border-bottom: 1px solid #e5e7eb;">Circunstância</th>
                                    <th style="padding: 0.5rem; text-align: left; border-bottom: 1px solid #e5e7eb;">Local</th>
                                    <th style="padding: 0.5rem; text-align: left; border-bottom: 1px solid #e5e7eb;">Coordenadas</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${trajeto.contacts.map((contact, index) => `
                                    <tr>
                                        <td style="padding: 0.5rem; border-bottom: 1px solid #f3f4f6;">${contact.time || 'N/A'}</td>
                                        <td style="padding: 0.5rem; border-bottom: 1px solid #f3f4f6;">${contact.circumstance || 'N/A'}</td>
                                        <td style="padding: 0.5rem; border-bottom: 1px solid #f3f4f6;">${contact.location || 'N/A'}</td>
                                        <td style="padding: 0.5rem; border-bottom: 1px solid #f3f4f6; font-size: 0.8125rem; color: #6b7280;">${contact.coordinates ? `${contact.coordinates.lat}, ${contact.coordinates.lng}` : 'N/A'}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
                ` : ''}
            `;
            
            container.innerHTML = html;
        }

        function formatDate(dateString) {
            if (!dateString) return 'N/A';
            try {
                const date = new Date(dateString);
                return date.toLocaleDateString('pt-PT') + ' ' + date.toLocaleTimeString('pt-PT', {hour: '2-digit', minute: '2-digit'});
            } catch (e) {
                return dateString;
            }
        }

        // GPS Analysis function
        function analyzeGPSTrajectory(trajetoId) {
            const modal = document.getElementById('gpsAnalysisModal');
            const contentContainer = document.getElementById('gpsAnalysisContent');
            
            // Show loading
            contentContainer.innerHTML = `
                <div style="text-align: center;">
                    <div style="display: inline-block; width: 32px; height: 32px; border: 3px solid #f3f4f6; border-radius: 50%; border-top-color: #f59e0b; animation: spin 1s linear infinite;"></div>
                    <p style="margin-top: 1rem; color: #6b7280;">A analisar trajeto GPS...</p>
                </div>
            `;
            
            modal.style.display = 'block';
            
            // Send AJAX request for analysis
            fetch(window.location.href, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=analyze_gps&trajeto_id=${encodeURIComponent(trajetoId)}&ajax=1`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayGPSAnalysis(data.analysis, data.trajectory);
                } else {
                    contentContainer.innerHTML = `
                        <div style="color: #dc2626; text-align: center;">
                            <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 1rem;"></i>
                            <p>Erro ao analisar trajeto: ${data.error}</p>
                        </div>
                    `;
                }
            })
            .catch(error => {
                contentContainer.innerHTML = `
                    <div style="color: #dc2626; text-align: center;">
                        <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 1rem;"></i>
                        <p>Erro de conexão: ${error.message}</p>
                    </div>
                `;
            });
        }

        function displayGPSAnalysis(analysis, trajectory) {
            const contentContainer = document.getElementById('gpsAnalysisContent');
            
            // Quality color based on score
            let qualityColor = '#dc2626'; // Red
            if (analysis.quality_rating === 'INVÁLIDO') qualityColor = '#991b1b'; // Dark red for invalid
            else if (analysis.quality_score >= 80) qualityColor = '#10b981'; // Green
            else if (analysis.quality_score >= 60) qualityColor = '#f59e0b'; // Yellow
            else if (analysis.quality_score >= 40) qualityColor = '#f97316'; // Orange
            
            const html = `
                ${analysis.quality_rating === 'INVÁLIDO' ? `
                <div style="background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%); border: 2px solid #dc2626; border-radius: 8px; padding: 1rem; margin-bottom: 2rem; text-align: center;">
                    <div style="color: #dc2626; font-size: 1.25rem; font-weight: bold; margin-bottom: 0.5rem;">
                        <i class="fas fa-exclamation-triangle" style="margin-right: 0.5rem;"></i>
                        TRAJETO INVÁLIDO - DADOS CORROMPIDOS
                    </div>
                    <div style="color: #991b1b; font-size: 0.875rem;">
                        Este trajeto contém dados GPS gravemente corrompidos e deve ser eliminado imediatamente.
                    </div>
                </div>
                ` : ''}
                <div style="margin-bottom: 2rem;">
                    <div style="display: flex; align-items: center; justify-content: center; gap: 1rem; margin-bottom: 1rem;">
                        <div style="text-align: center;">
                            <div style="position: relative; width: 100px; height: 100px; margin: 0 auto;">
                                <svg style="width: 100%; height: 100%; transform: rotate(-90deg);">
                                    <circle cx="50" cy="50" r="40" stroke="#e5e7eb" stroke-width="8" fill="none"></circle>
                                    <circle cx="50" cy="50" r="40" stroke="${qualityColor}" stroke-width="8" fill="none" 
                                            stroke-dasharray="${2 * Math.PI * 40}" 
                                            stroke-dashoffset="${2 * Math.PI * 40 * (1 - analysis.quality_score / 100)}"
                                            style="transition: stroke-dashoffset 1s ease-in-out;"></circle>
                                </svg>
                                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center;">
                                    <div style="font-size: 1.5rem; font-weight: bold; color: ${qualityColor};">${analysis.quality_score}</div>
                                    <div style="font-size: 0.75rem; color: #6b7280;">pontos</div>
                                </div>
                            </div>
                            <div style="margin-top: 0.5rem; font-weight: 600; color: ${qualityColor};">${analysis.quality_rating}</div>
                        </div>
                        <div style="flex: 1;">
                            <h6 style="color: #374151; font-weight: 600; margin-bottom: 0.5rem;">Resumo da Análise</h6>
                            <p style="color: #6b7280; font-size: 0.875rem; margin: 0;">
                                Trajeto com ${analysis.statistics.total_points} pontos GPS, 
                                ${analysis.statistics.gps_jumps} saltos detectados, 
                                distância recalculada: ${analysis.statistics.recalculated_distance_km} km
                            </p>
                        </div>
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin-bottom: 2rem;">
                    <div>
                        <h6 style="color: #374151; font-weight: 600; margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fas fa-chart-bar" style="color: #3b82f6;"></i>
                            Estatísticas GPS
                        </h6>
                        <table style="width: 100%; font-size: 0.875rem;">
                            <tr><td style="padding: 0.25rem 0; font-weight: 600;">Total de Pontos:</td><td style="padding: 0.25rem 0;">${analysis.statistics.total_points}</td></tr>
                            <tr><td style="padding: 0.25rem 0; font-weight: 600;">Pontos Válidos:</td><td style="padding: 0.25rem 0;">${analysis.statistics.valid_points}</td></tr>
                            <tr><td style="padding: 0.25rem 0; font-weight: 600;">Saltos GPS:</td><td style="padding: 0.25rem 0; color: ${analysis.statistics.gps_jumps > 0 ? '#dc2626' : '#10b981'};">${analysis.statistics.gps_jumps}</td></tr>
                            <tr><td style="padding: 0.25rem 0; font-weight: 600;">Velocidade Máxima:</td><td style="padding: 0.25rem 0;">${analysis.statistics.max_speed_kmh} km/h</td></tr>
                            <tr><td style="padding: 0.25rem 0; font-weight: 600;">Velocidade Média:</td><td style="padding: 0.25rem 0;">${analysis.statistics.avg_speed_kmh} km/h</td></tr>
                            <tr><td style="padding: 0.25rem 0; font-weight: 600;">Duração:</td><td style="padding: 0.25rem 0;">${analysis.statistics.time_span_minutes} min</td></tr>
                            <tr><td style="padding: 0.25rem 0; font-weight: 600;">Pontos Estacionários:</td><td style="padding: 0.25rem 0;">${analysis.statistics.stationary_points}</td></tr>
                        </table>
                    </div>
                    <div>
                        <h6 style="color: #374151; font-weight: 600; margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fas fa-route" style="color: #3b82f6;"></i>
                            Análise de Distância
                        </h6>
                        <table style="width: 100%; font-size: 0.875rem;">
                            <tr><td style="padding: 0.25rem 0; font-weight: 600;">Distância Recalculada:</td><td style="padding: 0.25rem 0; color: #10b981; font-weight: 600;">${analysis.statistics.recalculated_distance_km} km</td></tr>
                            <tr><td style="padding: 0.25rem 0; font-weight: 600;">Distância Original (Tabela):</td><td style="padding: 0.25rem 0; color: ${analysis.statistics.stored_distance_km > analysis.statistics.recalculated_distance_km * 2 ? '#dc2626' : '#6b7280'};">${analysis.statistics.stored_distance_km > 0 ? analysis.statistics.stored_distance_km + ' km' : 'N/A'}</td></tr>
                            <tr><td style="padding: 0.25rem 0; font-weight: 600;">Diferença:</td><td style="padding: 0.25rem 0; color: ${analysis.statistics.distance_difference_percent > 20 ? '#dc2626' : '#10b981'}; font-weight: 600;">${analysis.statistics.distance_difference_km || 0} km (${analysis.statistics.distance_difference_percent}%)</td></tr>
                            <tr><td style="padding: 0.25rem 0; font-weight: 600;">Densidade:</td><td style="padding: 0.25rem 0;">${Math.round(analysis.statistics.valid_points / analysis.statistics.recalculated_distance_km * 10) / 10} pontos/km</td></tr>
                            <tr><td style="padding: 0.25rem 0; font-weight: 600;">Fonte Original:</td><td style="padding: 0.25rem 0; font-size: 0.8rem; color: #6b7280;">${analysis.statistics.stored_distance_source}</td></tr>
                        </table>
                    </div>
                </div>

                ${analysis.issues.length > 0 ? `
                <div style="margin-bottom: 2rem;">
                    <h6 style="color: #374151; font-weight: 600; margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem;">
                        <i class="fas fa-exclamation-triangle" style="color: #dc2626;"></i>
                        Problemas Detectados
                    </h6>
                    <ul style="margin: 0; padding-left: 1.5rem;">
                        ${analysis.issues.map(issue => `<li style="color: #dc2626; margin-bottom: 0.5rem;">${issue}</li>`).join('')}
                    </ul>
                </div>
                ` : ''}

                ${analysis.gps_jumps.length > 0 ? `
                <div style="margin-bottom: 2rem;">
                    <h6 style="color: #374151; font-weight: 600; margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem;">
                        <i class="fas fa-bolt" style="color: #f59e0b;"></i>
                        Saltos GPS Detectados (primeiros 5)
                    </h6>
                    <div style="overflow-x: auto;">
                        <table style="width: 100%; font-size: 0.875rem; border-collapse: collapse;">
                            <thead>
                                <tr style="background-color: #f9fafb;">
                                    <th style="padding: 0.5rem; text-align: left; border-bottom: 1px solid #e5e7eb;">Ponto</th>
                                    <th style="padding: 0.5rem; text-align: left; border-bottom: 1px solid #e5e7eb;">Velocidade</th>
                                    <th style="padding: 0.5rem; text-align: left; border-bottom: 1px solid #e5e7eb;">Distância</th>
                                    <th style="padding: 0.5rem; text-align: left; border-bottom: 1px solid #e5e7eb;">Tempo</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${analysis.gps_jumps.slice(0, 5).map(jump => `
                                    <tr>
                                        <td style="padding: 0.5rem; border-bottom: 1px solid #f3f4f6;">#${jump.point_index}</td>
                                        <td style="padding: 0.5rem; border-bottom: 1px solid #f3f4f6; color: #dc2626; font-weight: 600;">${jump.speed_kmh} km/h</td>
                                        <td style="padding: 0.5rem; border-bottom: 1px solid #f3f4f6;">${Math.round(jump.distance_m)} m</td>
                                        <td style="padding: 0.5rem; border-bottom: 1px solid #f3f4f6;">${jump.time_diff_s}s</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
                ` : ''}

                <div>
                    <h6 style="color: #374151; font-weight: 600; margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem;">
                        <i class="fas fa-lightbulb" style="color: #10b981;"></i>
                        Recomendações
                    </h6>
                    <ul style="margin: 0; padding-left: 1.5rem;">
                        ${analysis.recommendations.map(rec => `<li style="color: #6b7280; margin-bottom: 0.5rem;">${rec}</li>`).join('')}
                    </ul>
                </div>
            `;
            
            contentContainer.innerHTML = html;
        }

        function closeGPSAnalysisModal() {
            document.getElementById('gpsAnalysisModal').style.display = 'none';
        }

        // Close modals when clicking outside
        window.onclick = function(event) {
            const deleteModal = document.getElementById('deleteModal');
            const trajetoModal = document.getElementById('trajetoModal');
            const gpsAnalysisModal = document.getElementById('gpsAnalysisModal');
            
            if (event.target === deleteModal) {
                closeDeleteModal();
            }
            if (event.target === trajetoModal) {
                closeTrajetoModal();
            }
            if (event.target === gpsAnalysisModal) {
                closeGPSAnalysisModal();
            }
        }

        // Add spin animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html> 