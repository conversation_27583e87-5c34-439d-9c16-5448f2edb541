// Server-side PHP application - Firebase is handled on the backend

// DataTables Initialization
$(document).ready(function() {
    // Initialize all DataTables
    $('.datatable').DataTable({
        responsive: true,
        pageLength: 10,
        language: {
            search: "Procurar:",
            lengthMenu: "Mostrar _MENU_ registros por página",
            info: "Mostrando página _PAGE_ de _PAGES_",
            paginate: {
                first: "Primeira",
                last: "Última",
                next: "Próxima",
                previous: "Anterior"
            }
        }
    });

    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Mobile sidebar toggle
    $('#sidebarToggle').on('click', function() {
        $('.sidebar').toggleClass('show');
    });
});

// Utility Functions
function formatDate(timestamp) {
    const date = timestamp.toDate();
    return date.toLocaleDateString('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

function showAlert(message, type = 'success') {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `;
    $('#alertContainer').html(alertHtml);
    setTimeout(() => {
        $('.alert').alert('close');
    }, 5000);
}

// Map Initialization
function initMap(containerId, center = { lat: -23.5505, lng: -46.6333 }, markers = []) {
    const map = L.map(containerId).setView([center.lat, center.lng], 13);
    
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
    }).addTo(map);

    markers.forEach(marker => {
        L.marker([marker.lat, marker.lng])
            .bindPopup(marker.popup)
            .addTo(map);
    });

    return map;
}

// Export functions
window.formatDate = formatDate;
window.showAlert = showAlert;
window.initMap = initMap; 